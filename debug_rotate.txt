FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
